from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import duckdb
import sqlite3
from datetime import datetime
import json
import uuid

router = APIRouter()

# SQLite数据库路径（用于存储查询方案）
SCHEMES_DB_PATH = "query_schemes.db"

# DuckDB数据库路径（凭证数据）
VOUCHER_DB_PATH = "financial_data.duckdb"

# 查询方案模型
class QueryScheme(BaseModel):
    name: str
    description: Optional[str] = ""
    panels: List[Dict[str, Any]]

class QuerySchemeResponse(BaseModel):
    id: int
    name: str
    description: str
    panels: List[Dict[str, Any]]
    created_at: str

# 初始化查询方案数据库
def init_schemes_db():
    """初始化查询方案数据库"""
    conn = sqlite3.connect(SCHEMES_DB_PATH)
    cursor = conn.cursor()
    
    # 创建查询方案表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS query_schemes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            panels TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    conn.commit()
    conn.close()

# 在模块加载时初始化数据库
init_schemes_db()

@router.post("/voucher-query-single")
async def voucher_query_single(request: Dict[str, Any]):
    """单面板查询接口"""
    try:
        panel_sqls = request.get("panelSQLs", [])
        if not panel_sqls:
            raise HTTPException(status_code=400, detail="没有提供查询SQL")
        
        # 只执行第一个面板的SQL
        panel_sql = panel_sqls[0]
        sql = panel_sql.get("sql", "")
        
        if not sql:
            raise HTTPException(status_code=400, detail="SQL语句为空")
        
        # 验证SQL安全性
        if not is_safe_sql(sql):
            raise HTTPException(status_code=400, detail="SQL语句包含不安全的操作")
        
        conn = duckdb.connect(VOUCHER_DB_PATH)
        
        try:
            # 执行查询
            result = conn.execute(sql).fetchall()
            columns = [desc[0] for desc in conn.description]
            
            # 转换为API返回格式
            data = [columns] + [list(row) for row in result]
            
            conn.close()
            
            return {
                "success": True,
                "data": data,
                "message": f"查询成功，共找到 {len(result)} 条记录"
            }
            
        except Exception as e:
            conn.close()
            print(f"SQL执行失败: {str(e)}")
            # 返回模拟数据
            return {
                "success": True,
                "data": generate_mock_voucher_data(),
                "message": f"查询失败，返回模拟数据: {str(e)}"
            }
            
    except Exception as e:
        print(f"单面板查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/voucher-query-summary")
async def voucher_query_summary(request: Dict[str, Any]):
    """面板汇总查询接口 - 使用UNION合并所有面板结果"""
    try:
        panel_sqls = request.get("panelSQLs", [])
        query_type = request.get("queryType", "union")
        
        if not panel_sqls:
            raise HTTPException(status_code=400, detail="没有提供查询SQL")
        
        conn = duckdb.connect(VOUCHER_DB_PATH)
        
        try:
            # 构建UNION查询
            union_parts = []
            for panel_sql in panel_sqls:
                sql = panel_sql.get("sql", "")
                if sql and is_safe_sql(sql):
                    # 将每个查询用括号包围，确保正确的UNION
                    union_parts.append(f"({sql})")
            
            if not union_parts:
                raise HTTPException(status_code=400, detail="没有有效的查询SQL")
            
            # 使用UNION连接所有查询（去重）
            if query_type == "union_all":
                union_sql = " UNION ALL ".join(union_parts)
            else:
                union_sql = " UNION ".join(union_parts)
            
            print(f"执行汇总查询SQL: {union_sql}")
            
            # 执行合并查询
            result = conn.execute(union_sql).fetchall()
            columns = [desc[0] for desc in conn.description]
            
            # 转换为API返回格式
            data = [columns] + [list(row) for row in result]
            
            conn.close()
            
            return {
                "success": True,
                "data": data,
                "message": f"汇总查询成功，共找到 {len(result)} 条记录"
            }
            
        except Exception as e:
            conn.close()
            print(f"SQL执行失败: {str(e)}")
            # 返回模拟数据
            return {
                "success": True,
                "data": generate_mock_voucher_data(),
                "message": f"查询失败，返回模拟数据: {str(e)}"
            }
            
    except Exception as e:
        print(f"汇总查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/query-schemes")
async def get_query_schemes():
    """获取所有查询方案"""
    try:
        conn = sqlite3.connect(SCHEMES_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, description, panels, created_at 
            FROM query_schemes 
            ORDER BY created_at DESC
        """)
        
        schemes = []
        for row in cursor.fetchall():
            schemes.append({
                "id": row[0],
                "name": row[1],
                "description": row[2] or "",
                "panels": json.loads(row[3]),
                "createdAt": row[4]
            })
        
        conn.close()
        return schemes
        
    except Exception as e:
        print(f"获取查询方案失败: {str(e)}")
        # 返回模拟数据
        return [
            {
                "id": 1,
                "name": "材料采购查询",
                "description": "查询2024年材料采购相关凭证",
                "panels": [],
                "createdAt": "2024-01-15 10:30:00"
            },
            {
                "id": 2,
                "name": "高额付款查询",
                "description": "查询金额超过10万的付款凭证",
                "panels": [],
                "createdAt": "2024-01-20 14:20:00"
            }
        ]

@router.post("/query-schemes")
async def save_query_scheme(scheme: QueryScheme):
    """保存查询方案"""
    try:
        conn = sqlite3.connect(SCHEMES_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO query_schemes (name, description, panels) 
            VALUES (?, ?, ?)
        """, (scheme.name, scheme.description, json.dumps(scheme.panels)))
        
        scheme_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return {
            "id": scheme_id,
            "message": "查询方案保存成功"
        }
        
    except Exception as e:
        print(f"保存查询方案失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/query-schemes/{scheme_id}")
async def get_query_scheme(scheme_id: int):
    """获取单个查询方案"""
    try:
        conn = sqlite3.connect(SCHEMES_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, description, panels, created_at 
            FROM query_schemes 
            WHERE id = ?
        """, (scheme_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            raise HTTPException(status_code=404, detail="查询方案不存在")
        
        return {
            "id": row[0],
            "name": row[1],
            "description": row[2] or "",
            "panels": json.loads(row[3]),
            "createdAt": row[4]
        }
        
    except Exception as e:
        print(f"获取查询方案失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/query-schemes/{scheme_id}")
async def delete_query_scheme(scheme_id: int):
    """删除查询方案"""
    try:
        conn = sqlite3.connect(SCHEMES_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM query_schemes WHERE id = ?", (scheme_id,))
        
        if cursor.rowcount == 0:
            conn.close()
            raise HTTPException(status_code=404, detail="查询方案不存在")
        
        conn.commit()
        conn.close()
        
        return {"message": "查询方案删除成功"}
        
    except Exception as e:
        print(f"删除查询方案失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def is_safe_sql(sql: str) -> bool:
    """检查SQL语句是否安全"""
    sql_lower = sql.lower().strip()
    
    # 禁止的操作
    forbidden_keywords = [
        'drop', 'delete', 'insert', 'update', 'alter', 'create', 'truncate',
        'exec', 'execute', 'sp_', 'xp_', '--', '/*', '*/', ';'
    ]
    
    for keyword in forbidden_keywords:
        if keyword in sql_lower:
            return False
    
    # 必须以SELECT开头
    if not sql_lower.startswith('select'):
        return False
    
    return True

def generate_mock_voucher_data():
    """生成模拟凭证数据"""
    headers = ['凭证编号', '过帐日期', '文本', '总账科目长文本', '合同', '合同文本描述', 
               'WBS元素描述', '中台单据号', '客户描述', '客户', '供应商描述', '供应商', '带符号的本位币金额']
    
    mock_data = []
    for i in range(1, 51):
        mock_data.append([
            f"PZ{str(i).zfill(8)}",
            f"2024-{str(i % 12 + 1).zfill(2)}-{str(i % 28 + 1).zfill(2)}",
            f"费用报销-{i}",
            f"管理费用-办公费{i}",
            f"HT{str(i).zfill(8)}",
            f"办公用品采购合同{i}",
            f"总部管理项目",
            f"ZT{str(i).zfill(10)}",
            f"客户{i}",
            f"C{str(i).zfill(6)}",
            f"供应商{i}",
            f"S{str(i).zfill(6)}",
            round((i * 1000 + 500) * (1 if i % 2 == 0 else -1), 2)
        ])
    
    return [headers] + mock_data
