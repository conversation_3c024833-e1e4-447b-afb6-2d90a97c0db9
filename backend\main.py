from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import duckdb
import pandas as pd
from datetime import datetime
import json
import os
import re

from . import ollama_api

app = FastAPI(title="财务台账查询系统", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(ollama_api.router, prefix="/ollama", tags=["Ollama AI Chat"])

# 数据库连接
DB_PATH = "financial_data.duckdb"

# 数据库管理请求模型
class DbAdminQueryRequest(BaseModel):
    table_name: str

# 凭证查询请求模型
class PanelSQL(BaseModel):
    panelIndex: int
    sql: str
    description: str

class VoucherQueryRequest(BaseModel):
    panelSQLs: List[PanelSQL]
    panels: List[Dict[str, Any]]

# 综合查询请求模型
class QueryRule(BaseModel):
    id: int
    table: str
    field: str
    operator: str
    value: str
    logic: str

class UniversalQueryRequest(BaseModel):
    rules: List[QueryRule]

class DbAdminUpdateRequest(BaseModel):
    table_name: str
    data: List[List[Any]]

# 请求模型
class QueryRequest(BaseModel):
    filters: Dict[str, Any]
    timestamp: str

# 响应模型
class QueryResponse(BaseModel):
    code: int
    message: str
    data: List[List[Any]]
    timestamp: str

# 初始化数据库和表结构
def init_database():
    """初始化数据库和表结构"""
    conn = duckdb.connect(DB_PATH)
    
    # 创建一体化合同台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS integrated_contract (
            search_key VARCHAR,
            organization_name VARCHAR,
            project_name VARCHAR,
            project_code VARCHAR,
            contract_name VARCHAR,
            contract_code VARCHAR,
            original_contract_code VARCHAR,
            contract_business_content VARCHAR,
            customer_name VARCHAR,
            customer_code VARCHAR,
            contract_type VARCHAR,
            contract_amount DOUBLE,
            tax_rate VARCHAR,
            settlement_amount DOUBLE,
            prepaid_amount DOUBLE,
            paid_amount DOUBLE,
            invoice_amount DOUBLE,
            payment_ratio DOUBLE,
            payable_balance DOUBLE,
            overdue_amount DOUBLE
        )
    """)
    
    # 创建专项储备表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS special_reserve (
            voucher_number VARCHAR,
            fiscal_year VARCHAR,
            profit_center VARCHAR,
            profit_center_desc VARCHAR,
            text VARCHAR,
            posting_date DATE,
            input_date DATE,
            safety_production_fee DOUBLE,
            type VARCHAR
        )
    """)
    
    # 创建主数据表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS master_data (
            project_code VARCHAR,
            profit_center VARCHAR,
            accounting_organization VARCHAR,
            profit_center_desc VARCHAR,
            profit_center_group_desc VARCHAR
        )
    """)
    
    # 创建付款台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS payment_ledger (
            fiscal_year VARCHAR,
            posting_date DATE,
            input_date DATE,
            supplier_type VARCHAR,
            voucher_number VARCHAR,
            profit_center VARCHAR,
            profit_center_desc VARCHAR,
            supplier VARCHAR,
            supplier_desc VARCHAR,
            contract VARCHAR,
            contract_desc VARCHAR,
            text VARCHAR,
            platform_document_number VARCHAR,
            total_payment_amount DOUBLE,
            performance_bond_deduction DOUBLE,
            supply_chain_factoring DOUBLE,
            cost_offset DOUBLE,
            this_profit_center DOUBLE,
            internal_bank_or_deposit DOUBLE,
            internal_bank_customer VARCHAR
        )
    """)
    
    # 创建保证金台账表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS guarantee_ledger (
            serial_number DOUBLE,
            organization VARCHAR,
            organization_name VARCHAR,
            project_code VARCHAR,
            project_name VARCHAR,
            currency VARCHAR,
            deadline TIMESTAMP,
            guarantee_type VARCHAR,
            receiving_party VARCHAR,
            receiving_party_group VARCHAR,
            actual_paid_amount DOUBLE,
            actual_recovered_amount DOUBLE,
            remaining_amount DOUBLE,
            responsible_person VARCHAR
        )
    """)

    # 创建科目对照表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS subject_mapping (
            id INTEGER PRIMARY KEY,
            subject_code VARCHAR,
            subject_name VARCHAR,
            mapping_code VARCHAR,
            mapping_name VARCHAR,
            category VARCHAR,
            status VARCHAR,
            created_date DATE,
            updated_date DATE,
            remark VARCHAR
        )
    """)

    # 创建异常数据表
    conn.execute("""
        CREATE TABLE IF NOT EXISTS exception_data (
            id INTEGER PRIMARY KEY,
            data_source VARCHAR,
            error_type VARCHAR,
            error_description VARCHAR,
            original_value VARCHAR,
            suggested_value VARCHAR,
            status VARCHAR,
            created_date DATE,
            processed_date DATE,
            processor VARCHAR,
            remark VARCHAR
        )
    """)

    conn.close()

# 构建查询条件
def build_where_clause(filters: Dict[str, Any], table_name: str) -> str:
    """构建WHERE子句"""
    conditions = []
    
    for key, value in filters.items():
        if not value:
            continue
            
        # 处理金额范围查询
        if key.endswith('Min') and value:
            field_name = key[:-3]  # 移除'Min'后缀
            conditions.append(f"{field_name} >= {float(value)}")
        elif key.endswith('Max') and value:
            field_name = key[:-3]  # 移除'Max'后缀
            conditions.append(f"{field_name} <= {float(value)}")
        # 处理日期范围查询
        elif isinstance(value, list) and len(value) == 2:
            if value[0] and value[1]:
                conditions.append(f"{key} BETWEEN '{value[0]}' AND '{value[1]}'")
        # 处理文本查询
        elif isinstance(value, str) and value.strip():
            conditions.append(f"{key} LIKE '%{value.strip()}%'")
        # 处理数字查询
        elif isinstance(value, (int, float)):
            conditions.append(f"{key} = {value}")
    
    return " AND ".join(conditions) if conditions else "1=1"

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库"""
    init_database()

@app.get("/")
async def root():
    return {"message": "财务台账查询系统API"}

@app.post("/api/query/integrated-contract", response_model=QueryResponse)
async def query_integrated_contract(request: QueryRequest):
    """查询一体化合同台账"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "integrated_contract")
        query = f"""
            SELECT * FROM integrated_contract 
            WHERE {where_clause}
            ORDER BY contract_code DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        # 构造返回数据，第一行为列名
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/special-reserve", response_model=QueryResponse)
async def query_special_reserve(request: QueryRequest):
    """查询专项储备"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "special_reserve")
        query = f"""
            SELECT * FROM special_reserve 
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/master-data", response_model=QueryResponse)
async def query_master_data(request: QueryRequest):
    """查询主数据"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "master_data")
        query = f"""
            SELECT * FROM master_data
            WHERE {where_clause}
            ORDER BY project_code
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/payment-ledger", response_model=QueryResponse)
async def query_payment_ledger(request: QueryRequest):
    """查询付款台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "payment_ledger")
        query = f"""
            SELECT * FROM payment_ledger
            WHERE {where_clause}
            ORDER BY posting_date DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/guarantee-ledger", response_model=QueryResponse)
async def query_guarantee_ledger(request: QueryRequest):
    """查询保证金台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "guarantee_ledger")
        query = f"""
            SELECT * FROM guarantee_ledger
            WHERE {where_clause}
            ORDER BY deadline DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 导入额外的API端点
from additional_endpoints import (
    query_internal_bank,
    query_internal_reconciliation,
    query_subcontractor_settlement,
    query_external_confirmation,
    query_payable_by_supplier,
    query_cost_ledger,
    query_receipt_ledger,
    query_fund_management
)

# 导入资金流动API
from capital_flow_api import router as capital_flow_router

# 导入薪酬个税API
from salary_tax_api import router as salary_tax_router

# 导入查询规则API
from query_rules_api import router as query_rules_router

# 导入凭证查询扩展API
from voucher_query_api import router as voucher_query_router

# 注册额外的API端点
@app.post("/api/query/internal-bank", response_model=QueryResponse)
async def api_query_internal_bank(request: QueryRequest):
    return await query_internal_bank(request)

@app.post("/api/query/internal-reconciliation", response_model=QueryResponse)
async def api_query_internal_reconciliation(request: QueryRequest):
    return await query_internal_reconciliation(request)

@app.post("/api/query/subcontractor-settlement", response_model=QueryResponse)
async def api_query_subcontractor_settlement(request: QueryRequest):
    return await query_subcontractor_settlement(request)

@app.post("/api/query/external-confirmation", response_model=QueryResponse)
async def api_query_external_confirmation(request: QueryRequest):
    return await query_external_confirmation(request)

@app.post("/api/query/payable-by-supplier", response_model=QueryResponse)
async def api_query_payable_by_supplier(request: QueryRequest):
    return await query_payable_by_supplier(request)

@app.post("/api/query/cost-ledger", response_model=QueryResponse)
async def api_query_cost_ledger(request: QueryRequest):
    return await query_cost_ledger(request)

@app.post("/api/query/receipt-ledger", response_model=QueryResponse)
async def api_query_receipt_ledger(request: QueryRequest):
    return await query_receipt_ledger(request)

@app.post("/api/query/fund-management", response_model=QueryResponse)
async def api_query_fund_management(request: QueryRequest):
    return await query_fund_management(request)

# 注册资金流动API路由
app.include_router(capital_flow_router)

# 注册薪酬个税API路由
app.include_router(salary_tax_router)

# 注册查询规则API路由
app.include_router(query_rules_router, prefix="/api", tags=["Query Rules"])

# 注册凭证查询扩展API路由
app.include_router(voucher_query_router, prefix="/api", tags=["Voucher Query"])

# 数据库管理API接口
@app.get("/api/db-admin/query")
async def db_admin_query(table_name: str):
    """数据库管理查询接口"""
    try:
        # 验证表名
        allowed_tables = ["subject_mapping", "exception_data"]
        if table_name not in allowed_tables:
            raise HTTPException(status_code=400, detail=f"不支持的表名: {table_name}")

        conn = duckdb.connect(DB_PATH)

        # 查询数据
        query = f"SELECT * FROM {table_name} ORDER BY id DESC LIMIT 1000"
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        # 构造返回数据，第一行为列名
        data = [columns] + [list(row) for row in result]

        conn.close()

        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/db-admin/update")
async def db_admin_update(request: DbAdminUpdateRequest):
    """数据库管理更新接口"""
    try:
        # 验证表名
        allowed_tables = ["subject_mapping", "exception_data"]
        if request.table_name not in allowed_tables:
            raise HTTPException(status_code=400, detail=f"不支持的表名: {request.table_name}")

        if not request.data or len(request.data) < 2:
            raise HTTPException(status_code=400, detail="数据格式错误，至少需要表头和一行数据")

        conn = duckdb.connect(DB_PATH)

        # 获取表头和数据
        headers = request.data[0]
        data_rows = request.data[1:]

        # 清空表数据
        conn.execute(f"DELETE FROM {request.table_name}")

        # 构建插入语句
        placeholders = ", ".join(["?" for _ in headers])
        insert_query = f"INSERT INTO {request.table_name} ({', '.join(headers)}) VALUES ({placeholders})"

        # 批量插入数据
        for row in data_rows:
            # 确保行数据长度与表头一致
            if len(row) != len(headers):
                # 补齐或截断数据
                if len(row) < len(headers):
                    row.extend([None] * (len(headers) - len(row)))
                else:
                    row = row[:len(headers)]
            conn.execute(insert_query, row)

        conn.close()

        return {"message": f"成功更新 {len(data_rows)} 条记录到表 {request.table_name}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

# 综合查询API接口
@app.post("/api/universal-query")
async def universal_query(request: UniversalQueryRequest):
    """综合查询接口 - 支持动态查询规则"""
    try:
        if not request.rules:
            raise HTTPException(status_code=400, detail="查询规则不能为空")

        conn = duckdb.connect(DB_PATH)

        # 构建查询语句
        query_parts = []
        params = []

        # 表名映射 - 确保表存在
        table_mapping = {
            'subject_mapping': 'subject_mapping',
            'exception_data': 'exception_data',
            'integrated_contract': 'integrated_contract',
            'master_data': 'master_data',
            'payment_ledger': 'payment_ledger',
            'guarantee_ledger': 'guarantee_ledger',
            'internal_bank': 'internal_bank',
            'internal_reconciliation': 'internal_reconciliation'
        }

        # 字段映射 - 确保字段存在
        field_mapping = {
            'subject_mapping': {
                'gl_account_text': 'gl_account_text',
                'category1': 'category1',
                'category2': 'category2',
                'direction': 'direction'
            },
            'exception_data': {
                'exception_type': 'exception_type',
                'description': 'description',
                'occur_date': 'occur_date',
                'status': 'status'
            },
            'integrated_contract': {
                'contract_number': 'contract_number',
                'contract_name': 'contract_name',
                'customer_name': 'customer_name',
                'contract_amount': 'contract_amount',
                'contract_type': 'contract_type'
            },
            'master_data': {
                'project_code': 'project_code',
                'profit_center': 'profit_center',
                'accounting_org': 'accounting_org',
                'profit_center_desc': 'profit_center_desc'
            }
        }

        # 获取所有涉及的表
        tables_used = set()
        for rule in request.rules:
            if rule.table in table_mapping:
                tables_used.add(rule.table)

        if not tables_used:
            raise HTTPException(status_code=400, detail="没有找到有效的查询表")

        # 如果只有一个表，直接查询
        if len(tables_used) == 1:
            table_name = list(tables_used)[0]
            actual_table = table_mapping[table_name]

            # 构建WHERE条件
            where_conditions = []
            for i, rule in enumerate(request.rules):
                if rule.table != table_name:
                    continue

                field_map = field_mapping.get(table_name, {})
                actual_field = field_map.get(rule.field, rule.field)

                condition = build_condition(actual_field, rule.operator, rule.value, params)
                if condition:
                    if i > 0 and where_conditions:
                        where_conditions.append(f" {rule.logic.upper()} ")
                    where_conditions.append(condition)

            where_clause = "".join(where_conditions) if where_conditions else "1=1"
            query = f"SELECT * FROM {actual_table} WHERE {where_clause} LIMIT 1000"

        else:
            # 多表查询 - 简化处理，返回模拟数据
            return {
                "success": True,
                "data": generate_mock_query_result(),
                "message": "多表查询功能开发中，返回模拟数据"
            }

        # 执行查询
        result = conn.execute(query, params).fetchall()
        columns = [desc[0] for desc in conn.description]

        # 构造返回数据
        data = [columns] + [list(row) for row in result]

        conn.close()

        return {
            "success": True,
            "data": data,
            "message": f"查询成功，共找到 {len(result)} 条记录"
        }

    except Exception as e:
        print(f"综合查询失败: {str(e)}")
        # 返回模拟数据作为fallback
        return {
            "success": True,
            "data": generate_mock_query_result(),
            "message": f"查询失败，返回模拟数据: {str(e)}"
        }

def build_condition(field: str, operator: str, value: str, params: List):
    """构建查询条件"""
    if operator == "eq":
        params.append(value)
        return f"{field} = ?"
    elif operator == "like":
        params.append(f"%{value}%")
        return f"{field} LIKE ?"
    elif operator == "gt":
        params.append(value)
        return f"{field} > ?"
    elif operator == "lt":
        params.append(value)
        return f"{field} < ?"
    elif operator == "gte":
        params.append(value)
        return f"{field} >= ?"
    elif operator == "lte":
        params.append(value)
        return f"{field} <= ?"
    elif operator == "ne":
        params.append(value)
        return f"{field} != ?"
    elif operator == "null":
        return f"{field} IS NULL"
    elif operator == "not_null":
        return f"{field} IS NOT NULL"
    else:
        return None

def generate_mock_query_result():
    """生成模拟查询结果"""
    headers = ['ID', '项目名称', '合同编号', '金额', '状态', '创建时间', '备注']
    mock_data = []

    for i in range(1, 21):
        mock_data.append([
            i,
            f"项目{i:03d}",
            f"HT{i:04d}",
            f"{(i * 50000 + 100000):.2f}",
            "进行中" if i % 3 == 0 else "已完成",
            f"2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
            f"这是第{i}个项目的备注信息"
        ])

    return [headers] + mock_data

# Snapshot API 接口
@app.post("/api/save-snapsheet")
async def save_snapsheet(request: List[Any]):
    """保存快照"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建快照表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS query_snapshots (
                id INTEGER PRIMARY KEY,
                snapshot_name VARCHAR,
                snapshot_data TEXT,
                remark VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        snapshot_data = request[0]
        remark = request[1] if len(request) > 1 else ""

        # 生成快照名称
        from datetime import datetime
        snapshot_name = f"查询快照_{datetime.now().year}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 保存快照
        conn.execute("""
            INSERT INTO query_snapshots (snapshot_name, snapshot_data, remark)
            VALUES (?, ?, ?)
        """, [snapshot_name, json.dumps(snapshot_data, ensure_ascii=False), remark])

        conn.close()

        return {"success": True, "message": "快照保存成功", "snapshot_name": snapshot_name}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存快照失败: {str(e)}")

@app.post("/api/get-snapsheet")
async def get_snapsheet(request: Dict[str, Any]):
    """获取快照"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建快照表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS query_snapshots (
                id INTEGER PRIMARY KEY,
                snapshot_name VARCHAR,
                snapshot_data TEXT,
                remark VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        period_id = request.get('periodId', 'max')
        year = request.get('year', datetime.now().year)
        
        if period_id == 'max':
            # 获取指定年份的最新快照
            result = conn.execute("""
                SELECT snapshot_data FROM query_snapshots
                WHERE strftime('%Y', created_at) = ?
                ORDER BY created_at DESC LIMIT 1
            """, [str(year)]).fetchone()
        else:
            # 获取指定ID的快照
            result = conn.execute("""
                SELECT snapshot_data FROM query_snapshots
                WHERE id = ?
            """, [period_id]).fetchone()

        conn.close()

        if result:
            return json.loads(result[0])
        else:
            # 返回默认快照结构
            return {
                "id": "default-query-workbook",
                "name": "综合查询结果",
                "sheets": {
                    "查询结果": {
                        "id": "sheet1",
                        "name": "查询结果",
                        "cellData": {}
                    }
                }
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取快照失败: {str(e)}")

@app.get("/api/snapshots/list")
async def list_snapshots(year: Optional[int] = None):
    """获取快照列表，支持按年份筛选"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建快照表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS query_snapshots (
                id INTEGER PRIMARY KEY,
                snapshot_name VARCHAR,
                snapshot_data TEXT,
                remark VARCHAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        if year:
            # 按年份筛选
            result = conn.execute("""
                SELECT id, snapshot_name, remark, created_at
                FROM query_snapshots
                WHERE strftime('%Y', created_at) = ?
                ORDER BY created_at DESC
            """, [str(year)]).fetchall()
        else:
            # 获取所有快照
            result = conn.execute("""
                SELECT id, snapshot_name, remark, created_at
                FROM query_snapshots
                ORDER BY created_at DESC
            """).fetchall()

        conn.close()

        snapshots = []
        for row in result:
            snapshots.append({
                "id": row[0],
                "name": row[1],
                "remark": row[2] or "",
                "created_at": row[3],
                "year": int(row[3].split("-")[0]) if row[3] else None
            })

        return snapshots
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取快照列表失败: {str(e)}")

@app.post("/api/delete-snapsheet")
async def delete_snapsheet(snapshot_id: int):
    """删除快照"""
    try:
        conn = duckdb.connect(DB_PATH)

        conn.execute("DELETE FROM query_snapshots WHERE id = ?", [snapshot_id])

        conn.close()

        return {"success": True, "message": "快照删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除快照失败: {str(e)}")

# 凭证查询API接口
@app.post("/api/voucher-query")
async def voucher_query(request: VoucherQueryRequest):
    """凭证查询接口 - 支持多面板独立查询和交集计算"""
    try:
        # 验证每个面板的SQL语句安全性
        for panel_sql in request.panelSQLs:
            if not is_safe_sql(panel_sql.sql):
                raise HTTPException(status_code=400, detail=f"面板{panel_sql.panelIndex + 1}的SQL语句包含不安全的操作")

        conn = duckdb.connect(DB_PATH)

        # 创建凭证表（如果不存在）
        conn.execute("""
            CREATE TABLE IF NOT EXISTS 明细帐 (
                id INTEGER PRIMARY KEY,
                凭证编号 VARCHAR,
                过帐日期 DATE,
                文本 VARCHAR,
                总账科目长文本 VARCHAR,
                合同 VARCHAR,
                合同文本描述 VARCHAR,
                WBS元素描述 VARCHAR,
                中台单据号 VARCHAR,
                客户描述 VARCHAR,
                客户 VARCHAR,
                供应商描述 VARCHAR,
                供应商 VARCHAR,
                带符号的本位币金额 DOUBLE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 检查表是否有数据，如果没有则插入模拟数据
        count_result = conn.execute("SELECT COUNT(*) FROM 明细帐").fetchone()
        if count_result[0] == 0:
            # 插入模拟凭证数据
            insert_mock_voucher_data_chinese(conn)

        # 执行每个面板的查询并计算交集
        try:
            panel_results = []
            all_columns = None
            
            # 执行每个面板的查询
            for panel_sql in request.panelSQLs:
                print(f"执行面板{panel_sql.panelIndex + 1}查询: {panel_sql.sql}")
                result = conn.execute(panel_sql.sql).fetchall()
                columns = [desc[0] for desc in conn.description]
                
                if all_columns is None:
                    all_columns = columns
                
                # 将结果转换为字典列表，便于计算交集
                result_dicts = []
                for row in result:
                    row_dict = {}
                    for i, col in enumerate(columns):
                        row_dict[col] = row[i]
                    result_dicts.append(row_dict)
                
                panel_results.append({
                    'panelIndex': panel_sql.panelIndex,
                    'description': panel_sql.description,
                    'data': result_dicts,
                    'count': len(result_dicts)
                })
                
                print(f"面板{panel_sql.panelIndex + 1}查询结果: {len(result_dicts)}条记录")

            # 计算交集
            if len(panel_results) == 0:
                intersection_result = []
            elif len(panel_results) == 1:
                intersection_result = panel_results[0]['data']
            else:
                # 多个面板求交集
                intersection_result = panel_results[0]['data']
                for i in range(1, len(panel_results)):
                    intersection_result = calculate_intersection(intersection_result, panel_results[i]['data'])
                    print(f"与面板{panel_results[i]['panelIndex'] + 1}求交集后: {len(intersection_result)}条记录")

            # 将交集结果转换为API返回格式
            if intersection_result:
                data_rows = []
                for row_dict in intersection_result:
                    row = []
                    for col in all_columns:
                        row.append(row_dict.get(col, ''))
                    data_rows.append(row)
                
                data = [all_columns] + data_rows
            else:
                data = [all_columns] if all_columns else []

            conn.close()

            # 构建查询摘要信息
            panel_summary = []
            for panel_result in panel_results:
                panel_summary.append(f"{panel_result['description']}: {panel_result['count']}条")
            
            summary_message = f"面板查询结果: {', '.join(panel_summary)}; 交集结果: {len(intersection_result)}条记录"

            return {
                "success": True,
                "data": data,
                "message": summary_message,
                "panelResults": panel_results,
                "intersectionCount": len(intersection_result)
            }
            
        except Exception as sql_error:
            conn.close()
            print(f"SQL执行失败: {str(sql_error)}")
            # 如果SQL执行失败，返回模拟数据
            mock_data = generate_mock_voucher_data_for_api()
            return {
                "success": True,
                "data": mock_data,
                "message": f"SQL执行失败，返回模拟数据: {str(sql_error)}",
                "panelResults": [],
                "intersectionCount": len(mock_data) - 1 if mock_data else 0
            }

    except Exception as e:
        print(f"凭证查询失败: {str(e)}")
        # 返回模拟数据作为fallback
        mock_data = generate_mock_voucher_data_for_api()
        return {
            "success": True,
            "data": mock_data,
            "message": f"查询失败，返回模拟数据: {str(e)}",
            "panelResults": [],
            "intersectionCount": len(mock_data) - 1 if mock_data else 0
        }

def is_safe_sql(sql: str) -> bool:
    """检查SQL语句是否安全"""
    # 转换为小写进行检查
    sql_lower = sql.lower().strip()

    # 禁止的操作
    forbidden_keywords = [
        'drop', 'delete', 'insert', 'update', 'alter', 'create', 'truncate',
        'exec', 'execute', 'sp_', 'xp_', '--', '/*', '*/', ';'
    ]

    for keyword in forbidden_keywords:
        if keyword in sql_lower:
            return False

    # 必须以SELECT开头
    if not sql_lower.startswith('select'):
        return False

    return True

def calculate_intersection(list1, list2):
    """计算两个字典列表的交集，基于所有字段的值"""
    if not list1 or not list2:
        return []
    
    # 将字典转换为可哈希的元组进行比较
    def dict_to_tuple(d):
        return tuple(sorted(d.items()))
    
    set1 = {dict_to_tuple(item) for item in list1}
    set2 = {dict_to_tuple(item) for item in list2}
    
    intersection = set1 & set2
    
    # 将元组转换回字典
    result = []
    for item_tuple in intersection:
        result.append(dict(item_tuple))
    
    return result

def insert_mock_voucher_data_chinese(conn):
    """插入中文模拟凭证数据到数据库"""
    reasons = ['材料采购', '工程款支付', '设备租赁', '人工费用', '管理费用', '财务费用', '销售费用', '其他费用']
    accounts = ['原材料', '应付账款', '银行存款', '应收账款', '固定资产', '累计折旧', '主营业务收入', '主营业务成本']
    customers = ['华为技术有限公司', '中国建筑股份有限公司', '中国石油天然气集团', '阿里巴巴集团', '腾讯控股有限公司']
    suppliers = ['中国中铁股份有限公司', '中国建材集团', '宝钢集团', '中国石化', '中国移动通信集团']
    projects = ['智慧城市建设项目', '数据中心建设项目', '5G网络建设项目', '绿色能源项目', '交通基础设施项目']

    import random

    for i in range(1, 201):  # 插入200条模拟数据
        random_reason = random.choice(reasons)
        random_account = random.choice(accounts)
        random_customer = random.choice(customers)
        random_supplier = random.choice(suppliers)
        random_project = random.choice(projects)

        conn.execute("""
            INSERT INTO 明细帐 (
                凭证编号, 过帐日期, 文本, 总账科目长文本, 合同, 合同文本描述, 
                WBS元素描述, 中台单据号, 客户描述, 客户, 供应商描述, 供应商, 带符号的本位币金额
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, [
            f"PZ{str(i).zfill(8)}",
            f"2024-{str(random.randint(1, 12)).zfill(2)}-{str(random.randint(1, 28)).zfill(2)}",
            f"{random_reason}-{i}",
            f"{random_account}-详细科目{i}",
            f"HT{str(i).zfill(8)}",
            f"合同{i}-{random_reason}合同",
            random_project,
            f"ZT{str(i).zfill(10)}",
            random_customer,
            f"C{str(i).zfill(6)}",
            random_supplier,
            f"S{str(i).zfill(6)}",
            round(random.uniform(-1000000, 1000000), 2)
        ])

def generate_mock_voucher_data_for_api():
    """生成模拟凭证数据用于API返回"""
    headers = ['凭证编号', '过帐日期', '文本', '总账科目长文本', '合同', '合同文本描述', 
               'WBS元素描述', '中台单据号', '客户描述', '客户', '供应商描述', '供应商', '带符号的本位币金额']
    
    reasons = ['材料采购', '工程款支付', '设备租赁', '人工费用', '管理费用', '财务费用', '销售费用', '其他费用']
    accounts = ['原材料', '应付账款', '银行存款', '应收账款', '固定资产', '累计折旧', '主营业务收入', '主营业务成本']
    customers = ['华为技术有限公司', '中国建筑股份有限公司', '中国石油天然气集团', '阿里巴巴集团', '腾讯控股有限公司']
    suppliers = ['中国中铁股份有限公司', '中国建材集团', '宝钢集团', '中国石化', '中国移动通信集团']
    projects = ['智慧城市建设项目', '数据中心建设项目', '5G网络建设项目', '绿色能源项目', '交通基础设施项目']

    import random
    
    mock_data = []
    for i in range(1, 51):  # 生成50条模拟数据
        random_reason = random.choice(reasons)
        random_account = random.choice(accounts)
        random_customer = random.choice(customers)
        random_supplier = random.choice(suppliers)
        random_project = random.choice(projects)

        mock_data.append([
            f"PZ{str(i).zfill(8)}",
            f"2024-{str(random.randint(1, 12)).zfill(2)}-{str(random.randint(1, 28)).zfill(2)}",
            f"{random_reason}-{i}",
            f"{random_account}-详细科目{i}",
            f"HT{str(i).zfill(8)}",
            f"合同{i}-{random_reason}合同",
            random_project,
            f"ZT{str(i).zfill(10)}",
            random_customer,
            f"C{str(i).zfill(6)}",
            random_supplier,
            f"S{str(i).zfill(6)}",
            round(random.uniform(-1000000, 1000000), 2)
        ])

    return [headers] + mock_data

def insert_mock_voucher_data(conn):
    """插入模拟凭证数据到数据库"""
    reasons = ['材料采购', '工程款支付', '设备租赁', '人工费用', '管理费用', '财务费用', '销售费用', '其他费用']
    accounts = ['原材料', '应付账款', '银行存款', '应收账款', '固定资产', '累计折旧', '主营业务收入', '主营业务成本']
    customers = ['华为技术有限公司', '中国建筑股份有限公司', '中国石油天然气集团', '阿里巴巴集团', '腾讯控股有限公司']
    suppliers = ['中国中铁股份有限公司', '中国建材集团', '宝钢集团', '中国石化', '中国移动通信集团']

    import random

    for i in range(1, 101):  # 插入100条模拟数据
        random_reason = random.choice(reasons)
        random_account = random.choice(accounts)
        random_customer = random.choice(customers)
        random_supplier = random.choice(suppliers)

        conn.execute("""
            INSERT INTO vouchers (
                voucher_number, date, reason, account_text, contract_number,
                platform_doc_number, customer_name, customer_code, supplier_name,
                supplier_code, amount, debit_amount, credit_amount
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, [
            f"PZ{str(i).zfill(6)}",
            f"2024-{str(random.randint(1, 12)).zfill(2)}-{str(random.randint(1, 28)).zfill(2)}",
            f"{random_reason}-{i}",
            f"{random_account}-详细科目{i}",
            f"HT{str(i).zfill(8)}",
            f"ZT{str(i).zfill(10)}",
            random_customer,
            f"C{str(i).zfill(6)}",
            random_supplier,
            f"S{str(i).zfill(6)}",
            round(random.uniform(1000, 1000000), 2),
            round(random.uniform(500, 500000), 2),
            round(random.uniform(500, 500000), 2)
        ])

def generate_mock_voucher_data_for_api():
    """生成API返回的模拟凭证数据"""
    headers = [
        'id', 'voucher_number', 'date', 'reason', 'account_text', 'contract_number',
        'platform_doc_number', 'customer_name', 'customer_code', 'supplier_name',
        'supplier_code', 'amount', 'debit_amount', 'credit_amount'
    ]

    reasons = ['材料采购', '工程款支付', '设备租赁', '人工费用', '管理费用', '财务费用', '销售费用', '其他费用']
    accounts = ['原材料', '应付账款', '银行存款', '应收账款', '固定资产', '累计折旧', '主营业务收入', '主营业务成本']
    customers = ['华为技术有限公司', '中国建筑股份有限公司', '中国石油天然气集团', '阿里巴巴集团', '腾讯控股有限公司']
    suppliers = ['中国中铁股份有限公司', '中国建材集团', '宝钢集团', '中国石化', '中国移动通信集团']

    import random

    mock_data = []
    for i in range(1, 51):  # 生成50条模拟数据
        random_reason = random.choice(reasons)
        random_account = random.choice(accounts)
        random_customer = random.choice(customers)
        random_supplier = random.choice(suppliers)

        mock_data.append([
            i,
            f"PZ{str(i).zfill(6)}",
            f"2024-{str(random.randint(1, 12)).zfill(2)}-{str(random.randint(1, 28)).zfill(2)}",
            f"{random_reason}-{i}",
            f"{random_account}-详细科目{i}",
            f"HT{str(i).zfill(8)}",
            f"ZT{str(i).zfill(10)}",
            random_customer,
            f"C{str(i).zfill(6)}",
            random_supplier,
            f"S{str(i).zfill(6)}",
            round(random.uniform(1000, 1000000), 2),
            round(random.uniform(500, 500000), 2),
            round(random.uniform(500, 500000), 2)
        ])

    return [headers] + mock_data

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
