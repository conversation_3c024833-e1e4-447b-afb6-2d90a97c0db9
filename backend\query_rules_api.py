"""
查询规则管理API
提供查询规则的保存、加载、删除等功能
使用SQLite数据库存储
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import sqlite3
import json
import os
from datetime import datetime

# 创建路由器
router = APIRouter()

# 数据库文件路径
DB_PATH = "query_rules.db"

# 数据模型
class QueryCondition(BaseModel):
    id: str
    field: str
    operator: str
    value: str = ""
    startDate: str = ""
    endDate: str = ""
    minAmount: Optional[float] = None
    maxAmount: Optional[float] = None
    logic: str = "AND"

class QueryPanel(BaseModel):
    id: str
    conditions: List[QueryCondition]

class QueryRule(BaseModel):
    name: str
    description: str = ""
    panels: List[QueryPanel]

class SaveQueryRuleRequest(BaseModel):
    name: str
    description: str = ""
    panels: List[QueryPanel]

class QueryRuleResponse(BaseModel):
    id: int
    name: str
    description: str
    panels: List[QueryPanel]
    created_at: str
    updated_at: str

# 数据库初始化
def init_database():
    """初始化数据库表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 创建查询规则表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS query_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT DEFAULT '',
            panels_json TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()

# 获取数据库连接
def get_db_connection():
    """获取数据库连接"""
    if not os.path.exists(DB_PATH):
        init_database()
    return sqlite3.connect(DB_PATH)

@router.post("/save-query-rule", response_model=dict)
async def save_query_rule(request: SaveQueryRuleRequest):
    """保存查询规则"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 将面板数据转换为JSON字符串
        panels_json = json.dumps([panel.dict() for panel in request.panels], ensure_ascii=False)
        
        # 检查规则名称是否已存在
        cursor.execute("SELECT id FROM query_rules WHERE name = ?", (request.name,))
        existing_rule = cursor.fetchone()
        
        if existing_rule:
            # 更新现有规则
            cursor.execute('''
                UPDATE query_rules 
                SET description = ?, panels_json = ?, updated_at = CURRENT_TIMESTAMP
                WHERE name = ?
            ''', (request.description, panels_json, request.name))
            rule_id = existing_rule[0]
            message = f"查询规则 '{request.name}' 已更新"
        else:
            # 插入新规则
            cursor.execute('''
                INSERT INTO query_rules (name, description, panels_json)
                VALUES (?, ?, ?)
            ''', (request.name, request.description, panels_json))
            rule_id = cursor.lastrowid
            message = f"查询规则 '{request.name}' 已保存"
        
        conn.commit()
        conn.close()
        
        return {
            "success": True,
            "message": message,
            "rule_id": rule_id
        }
        
    except sqlite3.IntegrityError as e:
        raise HTTPException(status_code=400, detail=f"规则名称已存在: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存失败: {str(e)}")

@router.get("/query-rules", response_model=List[QueryRuleResponse])
async def get_query_rules():
    """获取所有查询规则列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, description, panels_json, created_at, updated_at
            FROM query_rules
            ORDER BY updated_at DESC
        ''')
        
        rules = []
        for row in cursor.fetchall():
            rule_id, name, description, panels_json, created_at, updated_at = row
            
            # 解析面板JSON数据
            try:
                panels_data = json.loads(panels_json)
                panels = [QueryPanel(**panel) for panel in panels_data]
            except Exception as e:
                print(f"解析面板数据失败: {e}")
                panels = []
            
            rules.append(QueryRuleResponse(
                id=rule_id,
                name=name,
                description=description,
                panels=panels,
                created_at=created_at,
                updated_at=updated_at
            ))
        
        conn.close()
        return rules
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取查询规则失败: {str(e)}")

@router.get("/query-rules/{rule_id}", response_model=QueryRuleResponse)
async def get_query_rule(rule_id: int):
    """根据ID获取特定查询规则"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, description, panels_json, created_at, updated_at
            FROM query_rules
            WHERE id = ?
        ''', (rule_id,))
        
        row = cursor.fetchone()
        if not row:
            raise HTTPException(status_code=404, detail="查询规则不存在")
        
        rule_id, name, description, panels_json, created_at, updated_at = row
        
        # 解析面板JSON数据
        try:
            panels_data = json.loads(panels_json)
            panels = [QueryPanel(**panel) for panel in panels_data]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"解析面板数据失败: {str(e)}")
        
        conn.close()
        
        return QueryRuleResponse(
            id=rule_id,
            name=name,
            description=description,
            panels=panels,
            created_at=created_at,
            updated_at=updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取查询规则失败: {str(e)}")

@router.delete("/query-rules/{rule_id}")
async def delete_query_rule(rule_id: int):
    """删除查询规则"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查规则是否存在
        cursor.execute("SELECT name FROM query_rules WHERE id = ?", (rule_id,))
        rule = cursor.fetchone()
        
        if not rule:
            raise HTTPException(status_code=404, detail="查询规则不存在")
        
        # 删除规则
        cursor.execute("DELETE FROM query_rules WHERE id = ?", (rule_id,))
        conn.commit()
        conn.close()
        
        return {
            "success": True,
            "message": f"查询规则 '{rule[0]}' 已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除查询规则失败: {str(e)}")

# 多面板汇算查询接口
class MultiPanelQueryRequest(BaseModel):
    panels: List[QueryPanel]
    calculation_type: str = "intersection"  # intersection(交集) 或 union(并集)

@router.post("/multi-panel-calculation")
async def multi_panel_calculation(request: MultiPanelQueryRequest):
    """多面板汇算查询"""
    try:
        # 这里只是返回请求的数据结构，实际的数据库查询逻辑需要根据具体业务实现
        # 前端会处理具体的查询逻辑
        
        result_data = {
            "success": True,
            "message": "多面板汇算查询请求已接收",
            "panels_count": len(request.panels),
            "calculation_type": request.calculation_type,
            "panels": [panel.dict() for panel in request.panels]
        }
        
        return result_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"多面板汇算查询失败: {str(e)}")

# 初始化数据库
init_database()