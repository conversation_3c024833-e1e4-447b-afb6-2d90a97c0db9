<template>
    <div class="editor-page">
      <!-- 1. 添加一个按钮来触发文件选择 -->
      <div v-if="!documentFile">
        <button @click="showFileDialog = true">创建或打开文档</button>
      </div>
  
      <!-- 2. 当有文件时，渲染 DocumentHandler 组件 -->
      <DocumentHandler
        v-if="documentFile"
        :file="documentFile"
        style="height: 100vh; width: 100%"
      />
  
      <!-- 可以用一个对话框来让用户选择 -->
      <dialog :open="showFileDialog">
        <h2>新建文档</h2>
        <button @click="createNew('.docx')">新建 Word 文档</button>
        <button @click="createNew('.xlsx')">新建 Excel 表格</button>
        
        <h2>打开文档</h2>
        <button @click="openLocalFile">打开本地文件</button>
        
        <button @click="showFileDialog = false">关闭</button>
      </dialog>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue';
  // 导入 DocumentHandler 和需要的类型
  import DocumentHandler from '../components/DocumentHandler.vue'; // 确保路径正确
  import type { DocmentType } from '../utils/util'; // 确保路径正确
  
  const documentFile = ref<DocmentType | null>(null);
  const showFileDialog = ref(false);
  
  // 3. 实现新建和打开文件的逻辑
  const createNew = (extension: string) => {
    documentFile.value = {
      fileName: `新建文件${extension}`,
      file: null, // 新建文件时 file 为 null
    };
    showFileDialog.value = false;
  };
  
  const openLocalFile = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.docx,.xlsx,.pptx,.doc,.xls,.ppt'; // 限制文件类型
    
    input.onchange = (e) => {
      const target = e.target as HTMLInputElement;
      const file = target.files?.[0];
      if (file) {
        documentFile.value = {
          fileName: file.name,
          file: file,
        };
        showFileDialog.value = false;
      }
    };
    
    input.click();
  };
  </script>
  
  <style scoped>
  .editor-page {
    width: 100%;
    height: 100vh;
  }
  </style>